{"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON>", "useBackButton", "ref", "useEffect", "subscription", "addEventListener", "navigation", "current", "canGoBack", "goBack", "remove"], "sourceRoot": "../../src", "sources": ["useBackButton.native.tsx"], "mappings": "AAIA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,cAAc;AAE1C,eAAe,SAASC,aAAa,CACnCC,GAA2D,EAC3D;EACAH,KAAK,CAACI,SAAS,CAAC,MAAM;IACpB,MAAMC,YAAY,GAAGJ,WAAW,CAACK,gBAAgB,CAC/C,mBAAmB,EACnB,MAAM;MACJ,MAAMC,UAAU,GAAGJ,GAAG,CAACK,OAAO;MAE9B,IAAID,UAAU,IAAI,IAAI,EAAE;QACtB,OAAO,KAAK;MACd;MAEA,IAAIA,UAAU,CAACE,SAAS,EAAE,EAAE;QAC1BF,UAAU,CAACG,MAAM,EAAE;QAEnB,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC,CACF;IAED,OAAO,MAAML,YAAY,CAACM,MAAM,EAAE;EACpC,CAAC,EAAE,CAACR,GAAG,CAAC,CAAC;AACX"}