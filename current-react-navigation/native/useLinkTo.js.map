{"version": 3, "names": ["getActionFromState", "getStateFromPath", "NavigationContainerRefContext", "React", "LinkingContext", "useLinkTo", "navigation", "useContext", "linking", "linkTo", "useCallback", "to", "undefined", "Error", "navigate", "screen", "params", "startsWith", "options", "state", "config", "action", "dispatch", "reset"], "sourceRoot": "../../src", "sources": ["useLinkTo.tsx"], "mappings": "AAAA,SACEA,kBAAkB,EAClBC,gBAAgB,EAChBC,6BAA6B,QACxB,wBAAwB;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,cAAc,MAAM,kBAAkB;AAiB7C,eAAe,SAASC,SAAS,GAE7B;EACF,MAAMC,UAAU,GAAGH,KAAK,CAACI,UAAU,CAACL,6BAA6B,CAAC;EAClE,MAAMM,OAAO,GAAGL,KAAK,CAACI,UAAU,CAACH,cAAc,CAAC;EAEhD,MAAMK,MAAM,GAAGN,KAAK,CAACO,WAAW,CAC7BC,EAAiB,IAAK;IACrB,IAAIL,UAAU,KAAKM,SAAS,EAAE;MAC5B,MAAM,IAAIC,KAAK,CACb,kFAAkF,CACnF;IACH;IAEA,IAAI,OAAOF,EAAE,KAAK,QAAQ,EAAE;MAC1B;MACAL,UAAU,CAACQ,QAAQ,CAACH,EAAE,CAACI,MAAM,EAAEJ,EAAE,CAACK,MAAM,CAAC;MACzC;IACF;IAEA,IAAI,CAACL,EAAE,CAACM,UAAU,CAAC,GAAG,CAAC,EAAE;MACvB,MAAM,IAAIJ,KAAK,CAAE,iCAAgCF,EAAG,IAAG,CAAC;IAC1D;IAEA,MAAM;MAAEO;IAAQ,CAAC,GAAGV,OAAO;IAE3B,MAAMW,KAAK,GAAGD,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEjB,gBAAgB,GACnCiB,OAAO,CAACjB,gBAAgB,CAACU,EAAE,EAAEO,OAAO,CAACE,MAAM,CAAC,GAC5CnB,gBAAgB,CAACU,EAAE,EAAEO,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,MAAM,CAAC;IAEzC,IAAID,KAAK,EAAE;MACT,MAAME,MAAM,GAAGrB,kBAAkB,CAACmB,KAAK,EAAED,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,MAAM,CAAC;MAEzD,IAAIC,MAAM,KAAKT,SAAS,EAAE;QACxBN,UAAU,CAACgB,QAAQ,CAACD,MAAM,CAAC;MAC7B,CAAC,MAAM;QACLf,UAAU,CAACiB,KAAK,CAACJ,KAAK,CAAC;MACzB;IACF,CAAC,MAAM;MACL,MAAM,IAAIN,KAAK,CAAC,iDAAiD,CAAC;IACpE;EACF,CAAC,EACD,CAACL,OAAO,EAAEF,UAAU,CAAC,CACtB;EAED,OAAOG,MAAM;AACf"}