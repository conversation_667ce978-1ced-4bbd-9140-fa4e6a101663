{"version": 3, "names": ["CardStyleInterpolators", "HeaderStyleInterpolators", "TransitionPresets", "TransitionSpecs", "default", "createStackNavigator", "Header", "StackView", "CardAnimationContext", "GestureHandlerRefContext", "useCardAnimation", "useGestureHandlerRef"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": "AAAA,OAAO,KAAKA,sBAAsB,MAAM,4CAA4C;AACpF,OAAO,KAAKC,wBAAwB,MAAM,8CAA8C;AACxF,OAAO,KAAKC,iBAAiB,MAAM,uCAAuC;AAC1E,OAAO,KAAKC,eAAe,MAAM,qCAAqC;;AAEtE;AACA;AACA;AACA,SAASC,OAAO,IAAIC,oBAAoB,QAAQ,mCAAmC;;AAEnF;AACA;AACA;AACA,SAASD,OAAO,IAAIE,MAAM,QAAQ,uBAAuB;AACzD,SAASF,OAAO,IAAIG,SAAS,QAAQ,yBAAyB;;AAE9D;AACA;AACA;AACA,SACEP,sBAAsB,EACtBC,wBAAwB,EACxBC,iBAAiB,EACjBC,eAAe;;AAGjB;AACA;AACA;AACA,SAASC,OAAO,IAAII,oBAAoB,QAAQ,8BAA8B;AAC9E,SAASJ,OAAO,IAAIK,wBAAwB,QAAQ,kCAAkC;AACtF,SAASL,OAAO,IAAIM,gBAAgB,QAAQ,0BAA0B;AACtE,SAASN,OAAO,IAAIO,oBAAoB,QAAQ,8BAA8B;;AAE9E;AACA;AACA"}