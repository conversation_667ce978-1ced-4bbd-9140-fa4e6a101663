{"version": 3, "names": ["React", "CardAnimationContext", "useCardAnimation", "animation", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useCardAnimation.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,oBAAoB,MAAM,wBAAwB;AAEzD,eAAe,SAASC,gBAAgB,GAAG;EACzC,MAAMC,SAAS,GAAGH,KAAK,CAACI,UAAU,CAACH,oBAAoB,CAAC;EAExD,IAAIE,SAAS,KAAKE,SAAS,EAAE;IAC3B,MAAM,IAAIC,KAAK,CACb,4EAA4E,CAC7E;EACH;EAEA,OAAOH,SAAS;AAClB"}