let isInitialized = false;
let justFinishedEdgeGestureFromLeft = false;
let expectingTouchend = false;

/// This returns information if the user just performed edge gesture on iOS safari to navigate in the browser history.
export const getIsEdgeDragGesture = () => {
  return expectingTouchend || justFinishedEdgeGestureFromLeft;
};

// We need to manualy reset this flag after deciding if there should be animation for navigation.
export const resetExpectingTouchendWithDelay = () => {
  setTimeout(() => {
    expectingTouchend = false;
  }, 100);
};

export const maybeInitializeEdgeDragGestureMonitor = () => {
  if (isInitialized) {
    return;
  }
  isInitialized = true;
  let timer;

  // Gestures that would trigger navigation forward are broken on iOS safari. 
  // They don't have touchend event fired so we can look at expectingTouchEnd flag to detect if we should run animation. 
  const handleTouchStart = () => {
    expectingTouchend = true;
  };
  const handleTouchEnd = e => {
    var _e$changedTouches$;
    const pageX = (_e$changedTouches$ = e.changedTouches[0]) === null || _e$changedTouches$ === void 0 ? void 0 : _e$changedTouches$.pageX;
    // PageX for gesture that would trigger navigation back is negative. 
    if (pageX < 0) {
      if (timer) {
        clearTimeout(timer);
      }
      justFinishedEdgeGestureFromLeft = true;
      timer = setTimeout(() => justFinishedEdgeGestureFromLeft = false, 100);
    }
    expectingTouchend = false;
  };
  document.addEventListener('touchstart', handleTouchStart);
  document.addEventListener('touchend', handleTouchEnd);
};
