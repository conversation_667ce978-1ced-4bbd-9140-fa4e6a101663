{"version": 3, "names": ["NavigationContext", "useRoute", "React", "getScrollableNode", "ref", "current", "getScrollResponder", "getNode", "useScrollToTop", "navigation", "useContext", "route", "undefined", "Error", "useEffect", "tabNavigations", "currentNavigation", "getState", "type", "push", "getParent", "length", "unsubscribers", "map", "tab", "addListener", "e", "isFocused", "<PERSON><PERSON><PERSON><PERSON>", "includes", "routes", "key", "requestAnimationFrame", "scrollable", "defaultPrevented", "scrollToTop", "scrollTo", "y", "animated", "scrollToOffset", "offset", "scrollResponderScrollTo", "for<PERSON>ach", "unsubscribe"], "sourceRoot": "../../src", "sources": ["useScrollToTop.tsx"], "mappings": ";;AAAA,SAEEA,iBAAiB,EAGjBC,QAAQ,QACH,wBAAwB;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAiB9B,SAASC,iBAAiBA,CAACC,GAAuC,EAAE;EAClE,IAAIA,GAAG,CAACC,OAAO,IAAI,IAAI,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,IACE,aAAa,IAAID,GAAG,CAACC,OAAO,IAC5B,UAAU,IAAID,GAAG,CAACC,OAAO,IACzB,gBAAgB,IAAID,GAAG,CAACC,OAAO,IAC/B,yBAAyB,IAAID,GAAG,CAACC,OAAO,EACxC;IACA;IACA,OAAOD,GAAG,CAACC,OAAO;EACpB,CAAC,MAAM,IAAI,oBAAoB,IAAID,GAAG,CAACC,OAAO,EAAE;IAC9C;IACA;IACA,OAAOD,GAAG,CAACC,OAAO,CAACC,kBAAkB,CAAC,CAAC;EACzC,CAAC,MAAM,IAAI,SAAS,IAAIF,GAAG,CAACC,OAAO,EAAE;IACnC;IACA;IACA;IACA;IACA,OAAOD,GAAG,CAACC,OAAO,CAACE,OAAO,CAAC,CAAC;EAC9B,CAAC,MAAM;IACL,OAAOH,GAAG,CAACC,OAAO;EACpB;AACF;AAEA,OAAO,SAASG,cAAcA,CAACJ,GAAuC,EAAE;EACtE,MAAMK,UAAU,GAAGP,KAAK,CAACQ,UAAU,CAACV,iBAAiB,CAAC;EACtD,MAAMW,KAAK,GAAGV,QAAQ,CAAC,CAAC;EAExB,IAAIQ,UAAU,KAAKG,SAAS,EAAE;IAC5B,MAAM,IAAIC,KAAK,CACb,kFACF,CAAC;EACH;EAEAX,KAAK,CAACY,SAAS,CAAC,MAAM;IACpB,MAAMC,cAA+C,GAAG,EAAE;IAC1D,IAAIC,iBAAiB,GAAGP,UAAU;IAClC;IACA;IACA,OAAOO,iBAAiB,EAAE;MACxB,IAAIA,iBAAiB,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,KAAK,KAAK,EAAE;QAC/CH,cAAc,CAACI,IAAI,CAACH,iBAAiB,CAAC;MACxC;MAEAA,iBAAiB,GAAGA,iBAAiB,CAACI,SAAS,CAAC,CAAC;IACnD;IAEA,IAAIL,cAAc,CAACM,MAAM,KAAK,CAAC,EAAE;MAC/B;IACF;IAEA,MAAMC,aAAa,GAAGP,cAAc,CAACQ,GAAG,CAAEC,GAAG,IAAK;MAChD,OAAOA,GAAG,CAACC,WAAW;MACpB;MACA;MACA;MACA,UAAU,EACTC,CAA6B,IAAK;QACjC;QACA,MAAMC,SAAS,GAAGlB,UAAU,CAACkB,SAAS,CAAC,CAAC;;QAExC;QACA;QACA,MAAMC,OAAO,GACXb,cAAc,CAACc,QAAQ,CAACpB,UAAU,CAAC,IACnCA,UAAU,CAACQ,QAAQ,CAAC,CAAC,CAACa,MAAM,CAAC,CAAC,CAAC,CAACC,GAAG,KAAKpB,KAAK,CAACoB,GAAG;;QAEnD;QACA;QACAC,qBAAqB,CAAC,MAAM;UAC1B,MAAMC,UAAU,GAAG9B,iBAAiB,CAACC,GAAG,CAAsB;UAE9D,IAAIuB,SAAS,IAAIC,OAAO,IAAIK,UAAU,IAAI,CAACP,CAAC,CAACQ,gBAAgB,EAAE;YAC7D,IAAI,aAAa,IAAID,UAAU,EAAE;cAC/BA,UAAU,CAACE,WAAW,CAAC,CAAC;YAC1B,CAAC,MAAM,IAAI,UAAU,IAAIF,UAAU,EAAE;cACnCA,UAAU,CAACG,QAAQ,CAAC;gBAAEC,CAAC,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAK,CAAC,CAAC;YAC/C,CAAC,MAAM,IAAI,gBAAgB,IAAIL,UAAU,EAAE;cACzCA,UAAU,CAACM,cAAc,CAAC;gBAAEC,MAAM,EAAE,CAAC;gBAAEF,QAAQ,EAAE;cAAK,CAAC,CAAC;YAC1D,CAAC,MAAM,IAAI,yBAAyB,IAAIL,UAAU,EAAE;cAClDA,UAAU,CAACQ,uBAAuB,CAAC;gBAAEJ,CAAC,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAK,CAAC,CAAC;YAC9D;UACF;QACF,CAAC,CAAC;MACJ,CACF,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,MAAM;MACXhB,aAAa,CAACoB,OAAO,CAAEC,WAAW,IAAKA,WAAW,CAAC,CAAC,CAAC;IACvD,CAAC;EACH,CAAC,EAAE,CAAClC,UAAU,EAAEL,GAAG,EAAEO,KAAK,CAACoB,GAAG,CAAC,CAAC;AAClC", "ignoreList": []}