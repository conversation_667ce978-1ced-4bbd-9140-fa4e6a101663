{"version": 3, "names": ["createComponentForStaticNavigation", "createPathConfigForStaticNavigation", "React", "NavigationContainer", "jsx", "_jsx", "createStaticNavigation", "tree", "Component", "Navigation", "linking", "rest", "ref", "linkingConfig", "useMemo", "screens", "initialRouteName", "config", "enabled", "path", "memoizedLinking", "undefined", "Error", "children", "forwardRef"], "sourceRoot": "../../src", "sources": ["createStaticNavigation.tsx"], "mappings": ";;AAAA,SACEA,kCAAkC,EAClCC,mCAAmC,QAI9B,wBAAwB;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,mBAAmB,QAAQ,0BAAuB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AA8B5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAACC,IAAqC,EAAE;EAC5E,MAAMC,SAAS,GAAGR,kCAAkC,CAACO,IAAI,EAAE,eAAe,CAAC;EAE3E,SAASE,UAAUA,CACjB;IAAEC,OAAO;IAAE,GAAGC;EAAY,CAAC,EAC3BC,GAAqD,EACrD;IACA,MAAMC,aAAa,GAAGX,KAAK,CAACY,OAAO,CAAC,MAAM;MACxC,MAAMC,OAAO,GAAGd,mCAAmC,CACjDM,IAAI,EACJ;QAAES,gBAAgB,EAAEN,OAAO,EAAEO,MAAM,EAAED;MAAiB,CAAC,EACvDN,OAAO,EAAEQ,OAAO,KAAK,MACvB,CAAC;MAED,IAAI,CAACH,OAAO,EAAE;MAEd,OAAO;QACLI,IAAI,EAAET,OAAO,EAAEO,MAAM,EAAEE,IAAI;QAC3BH,gBAAgB,EAAEN,OAAO,EAAEO,MAAM,EAAED,gBAAgB;QACnDD;MACF,CAAC;IACH,CAAC,EAAE,CACDL,OAAO,EAAEQ,OAAO,EAChBR,OAAO,EAAEO,MAAM,EAAEE,IAAI,EACrBT,OAAO,EAAEO,MAAM,EAAED,gBAAgB,CAClC,CAAC;IAEF,MAAMI,eAAe,GAAGlB,KAAK,CAACY,OAAO,CAAC,MAAM;MAC1C,IAAI,CAACJ,OAAO,EAAE;QACZ,OAAOW,SAAS;MAClB;MAEA,MAAMH,OAAO,GACX,OAAOR,OAAO,CAACQ,OAAO,KAAK,SAAS,GAChCR,OAAO,CAACQ,OAAO,GACfL,aAAa,EAAEE,OAAO,IAAI,IAAI;MAEpC,OAAO;QACL,GAAGL,OAAO;QACVQ,OAAO;QACPD,MAAM,EAAEJ;MACV,CAAC;IACH,CAAC,EAAE,CAACH,OAAO,EAAEG,aAAa,CAAC,CAAC;IAE5B,IAAIH,OAAO,EAAEQ,OAAO,KAAK,IAAI,IAAIL,aAAa,EAAEE,OAAO,IAAI,IAAI,EAAE;MAC/D,MAAM,IAAIO,KAAK,CACb,gFAAgF,GAC9E,kBAAkB,GAClB,uEAAuE,GACvE,2EAA2E,GAC3E,gFACJ,CAAC;IACH;IAEA,oBACEjB,IAAA,CAACF,mBAAmB;MAAA,GAAKQ,IAAI;MAAEC,GAAG,EAAEA,GAAI;MAACF,OAAO,EAAEU,eAAgB;MAAAG,QAAA,eAChElB,IAAA,CAACG,SAAS,IAAE;IAAC,CACM,CAAC;EAE1B;EAEA,oBAAON,KAAK,CAACsB,UAAU,CAACf,UAAU,CAAC;AACrC", "ignoreList": []}