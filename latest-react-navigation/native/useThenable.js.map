{"version": 3, "names": ["React", "useThenable", "create", "promise", "useState", "initialState", "undefined", "then", "result", "state", "setState", "resolved", "useEffect", "cancelled", "resolve"], "sourceRoot": "../../src", "sources": ["useThenable.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAO,SAASC,WAAWA,CAAIC,MAA4B,EAAE;EAC3D,MAAM,CAACC,OAAO,CAAC,GAAGH,KAAK,CAACI,QAAQ,CAACF,MAAM,CAAC;EAExC,IAAIG,YAAsC,GAAG,CAAC,KAAK,EAAEC,SAAS,CAAC;;EAE/D;EACA;EACAH,OAAO,CAACI,IAAI,CAAEC,MAAM,IAAK;IACvBH,YAAY,GAAG,CAAC,IAAI,EAAEG,MAAM,CAAC;EAC/B,CAAC,CAAC;EAEF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGV,KAAK,CAACI,QAAQ,CAACC,YAAY,CAAC;EACtD,MAAM,CAACM,QAAQ,CAAC,GAAGF,KAAK;EAExBT,KAAK,CAACY,SAAS,CAAC,MAAM;IACpB,IAAIC,SAAS,GAAG,KAAK;IAErB,MAAMC,OAAO,GAAG,MAAAA,CAAA,KAAY;MAC1B,IAAIN,MAAM;MAEV,IAAI;QACFA,MAAM,GAAG,MAAML,OAAO;MACxB,CAAC,SAAS;QACR,IAAI,CAACU,SAAS,EAAE;UACdH,QAAQ,CAAC,CAAC,IAAI,EAAEF,MAAM,CAAC,CAAC;QAC1B;MACF;IACF,CAAC;IAED,IAAI,CAACG,QAAQ,EAAE;MACbG,OAAO,CAAC,CAAC;IACX;IAEA,OAAO,MAAM;MACXD,SAAS,GAAG,IAAI;IAClB,CAAC;EACH,CAAC,EAAE,CAACV,OAAO,EAAEQ,QAAQ,CAAC,CAAC;EAEvB,OAAOF,KAAK;AACd", "ignoreList": []}