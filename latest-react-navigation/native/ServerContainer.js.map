{"version": 3, "names": ["CurrentRenderContext", "React", "ServerContext", "jsx", "_jsx", "ServerContainer", "forwardRef", "children", "location", "ref", "useEffect", "console", "error", "current", "value", "getCurrentOptions", "options", "Provider"], "sourceRoot": "../../src", "sources": ["ServerContainer.tsx"], "mappings": ";;AAAA,SAASA,oBAAoB,QAAQ,wBAAwB;AAC7D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAa,QAAgC,oBAAiB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAOxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,gBAAGJ,KAAK,CAACK,UAAU,CAAC,SAASD,eAAeA,CACtE;EAAEE,QAAQ;EAAEC;AAAgB,CAAC,EAC7BC,GAAkC,EAClC;EACAR,KAAK,CAACS,SAAS,CAAC,MAAM;IACpBC,OAAO,CAACC,KAAK,CACX,sFACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,OAA6B,GAAG,CAAC,CAAC;EAExC,IAAIJ,GAAG,EAAE;IACP,MAAMK,KAAK,GAAG;MACZC,iBAAiBA,CAAA,EAAG;QAClB,OAAOF,OAAO,CAACG,OAAO;MACxB;IACF,CAAC;;IAED;IACA;IACA;IACA;IACA,IAAI,OAAOP,GAAG,KAAK,UAAU,EAAE;MAC7BA,GAAG,CAACK,KAAK,CAAC;IACZ,CAAC,MAAM;MACLL,GAAG,CAACI,OAAO,GAAGC,KAAK;IACrB;EACF;EAEA;IAAA;IACE;IACAV,IAAA,CAACF,aAAa,CAACe,QAAQ;MAACH,KAAK,EAAE;QAAEN;MAAS,CAAE;MAAAD,QAAA,eAC1CH,IAAA,CAACJ,oBAAoB,CAACiB,QAAQ;QAACH,KAAK,EAAED,OAAQ;QAAAN,QAAA,EAC3CA;MAAQ,CACoB;IAAC,CACV;EAAC;AAE7B,CAAC,CAAC", "ignoreList": []}