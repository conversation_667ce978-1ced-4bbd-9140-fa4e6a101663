{"version": 3, "names": ["getPathFromState", "NavigationContainerRefContext", "NavigationHelpersContext", "React", "Platform", "LinkingContext", "getStateFromParams", "params", "state", "screen", "routes", "name", "undefined", "useLinkProps", "href", "action", "root", "useContext", "navigation", "options", "onPress", "e", "<PERSON><PERSON><PERSON><PERSON>", "OS", "preventDefault", "hasModifierKey", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "isLeftClick", "button", "isSelfTarget", "currentTarget", "includes", "target", "dispatch", "Error", "navigate", "getPathFromStateHelper", "config", "role"], "sourceRoot": "../../src", "sources": ["useLinkProps.tsx"], "mappings": ";;AAAA,SACEA,gBAAgB,EAEhBC,6BAA6B,EAC7BC,wBAAwB,QAGnB,wBAAwB;AAE/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAAqCC,QAAQ,QAAQ,cAAc;AAEnE,SAASC,cAAc,QAAQ,qBAAkB;AAqBjD,MAAMC,kBAAkB,GACtBC,MAAwD,IACQ;EAChE,IAAIA,MAAM,EAAEC,KAAK,EAAE;IACjB,OAAOD,MAAM,CAACC,KAAK;EACrB;EAEA,IAAID,MAAM,EAAEE,MAAM,EAAE;IAClB,OAAO;MACLC,MAAM,EAAE,CACN;QACEC,IAAI,EAAEJ,MAAM,CAACE,MAAM;QACnBF,MAAM,EAAEA,MAAM,CAACA,MAAM;QACrB;QACAC,KAAK,EAAED,MAAM,CAACE,MAAM,GAChBH,kBAAkB,CAChBC,MAAM,CAACA,MAGT,CAAC,GACDK;MACN,CAAC;IAEL,CAAC;EACH;EAEA,OAAOA,SAAS;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAAkD;EAC5EJ,MAAM;EACNF,MAAM;EACNO,IAAI;EACJC;AACoB,CAAC,EAAE;EACvB,MAAMC,IAAI,GAAGb,KAAK,CAACc,UAAU,CAAChB,6BAA6B,CAAC;EAC5D,MAAMiB,UAAU,GAAGf,KAAK,CAACc,UAAU,CAACf,wBAAwB,CAAC;EAC7D,MAAM;IAAEiB;EAAQ,CAAC,GAAGhB,KAAK,CAACc,UAAU,CAACZ,cAAc,CAAC;EAEpD,MAAMe,OAAO,GACXC,CAA2E,IACxE;IACH,IAAIC,YAAY,GAAG,KAAK;IAExB,IAAIlB,QAAQ,CAACmB,EAAE,KAAK,KAAK,IAAI,CAACF,CAAC,EAAE;MAC/BA,CAAC,EAAEG,cAAc,GAAG,CAAC;MACrBF,YAAY,GAAG,IAAI;IACrB,CAAC,MAAM;MACL;MACA,MAAMG,cAAc,GACjB,SAAS,IAAIJ,CAAC,IAAIA,CAAC,CAACK,OAAO,IAC3B,QAAQ,IAAIL,CAAC,IAAIA,CAAC,CAACM,MAAO,IAC1B,SAAS,IAAIN,CAAC,IAAIA,CAAC,CAACO,OAAQ,IAC5B,UAAU,IAAIP,CAAC,IAAIA,CAAC,CAACQ,QAAS;;MAEjC;MACA,MAAMC,WAAW,GACf,QAAQ,IAAIT,CAAC,GAAGA,CAAC,CAACU,MAAM,IAAI,IAAI,IAAIV,CAAC,CAACU,MAAM,KAAK,CAAC,GAAG,IAAI;;MAE3D;MACA,MAAMC,YAAY,GAChBX,CAAC,CAACY,aAAa,IAAI,QAAQ,IAAIZ,CAAC,CAACY,aAAa,GAC1C,CAACrB,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAACsB,QAAQ,CAACb,CAAC,CAACY,aAAa,CAACE,MAAM,CAAC,GAC9D,IAAI;MAEV,IAAI,CAACV,cAAc,IAAIK,WAAW,IAAIE,YAAY,EAAE;QAClDX,CAAC,CAACG,cAAc,GAAG,CAAC;QACpBF,YAAY,GAAG,IAAI;MACrB;IACF;IAEA,IAAIA,YAAY,EAAE;MAChB,IAAIP,MAAM,EAAE;QACV,IAAIG,UAAU,EAAE;UACdA,UAAU,CAACkB,QAAQ,CAACrB,MAAM,CAAC;QAC7B,CAAC,MAAM,IAAIC,IAAI,EAAE;UACfA,IAAI,CAACoB,QAAQ,CAACrB,MAAM,CAAC;QACvB,CAAC,MAAM;UACL,MAAM,IAAIsB,KAAK,CACb,kFACF,CAAC;QACH;MACF,CAAC,MAAM;QACL;QACAnB,UAAU,EAAEoB,QAAQ,CAAC7B,MAAM,EAAEF,MAAM,CAAC;MACtC;IACF;EACF,CAAC;EAED,MAAMgC,sBAAsB,GAAGpB,OAAO,EAAEnB,gBAAgB,IAAIA,gBAAgB;EAE5E,OAAO;IACLc,IAAI,EACFA,IAAI,KACHV,QAAQ,CAACmB,EAAE,KAAK,KAAK,IAAId,MAAM,IAAI,IAAI,GACpC8B,sBAAsB,CACpB;MACE7B,MAAM,EAAE,CACN;QACE;QACAC,IAAI,EAAEF,MAAM;QACZ;QACAF,MAAM,EAAEA,MAAM;QACd;QACAC,KAAK,EAAEF,kBAAkB,CAACC,MAAM;MAClC,CAAC;IAEL,CAAC,EACDY,OAAO,EAAEqB,MACX,CAAC,GACD5B,SAAS,CAAC;IAChB6B,IAAI,EAAE,MAAe;IACrBrB;EACF,CAAC;AACH", "ignoreList": []}