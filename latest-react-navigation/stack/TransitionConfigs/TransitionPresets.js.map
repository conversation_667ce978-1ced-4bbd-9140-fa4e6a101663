{"version": 3, "names": ["Platform", "forBottomSheetAndroid", "forFadeFromBottomAndroid", "forFadeFromCenter", "forFadeCard", "forFadeFromRightAndroid", "forHorizontalIOS", "forHorizontalIOSInverted", "forModalPresentationIOS", "forRevealFromBottomAndroid", "forScaleFromCenterAndroid", "forVerticalIOS", "forFade", "BottomSheetSlideInSpec", "BottomSheetSlideOutSpec", "FadeInFromBottomAndroidSpec", "FadeOutToBottomAndroidSpec", "RevealFromBottomAndroidSpec", "ScaleFromCenterAndroidSpec", "TransitionIOSSpec", "ANDROID_VERSION_PIE", "ANDROID_VERSION_10", "ANDROID_VERSION_14", "SlideFromRightIOS", "gestureDirection", "transitionSpec", "open", "close", "cardStyleInterpolator", "headerStyleInterpolator", "ModalSlideFromBottomIOS", "ModalPresentationIOS", "FadeFromBottomAndroid", "RevealFromBottomAndroid", "ScaleFromCenterAndroid", "FadeFromRightAndroid", "BottomSheetAndroid", "ModalFadeTransition", "DefaultTransition", "select", "ios", "android", "Number", "Version", "default", "ModalTransition", "SlideFromLeftIOS"], "sourceRoot": "../../../src", "sources": ["TransitionConfigs/TransitionPresets.tsx"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,cAAc;AAGvC,SACEC,qBAAqB,EACrBC,wBAAwB,EACxBC,iBAAiB,IAAIC,WAAW,EAChCC,uBAAuB,EACvBC,gBAAgB,EAChBC,wBAAwB,EACxBC,uBAAuB,EACvBC,0BAA0B,EAC1BC,yBAAyB,EACzBC,cAAc,QACT,6BAA0B;AACjC,SAASC,OAAO,QAAQ,+BAA4B;AACpD,SACEC,sBAAsB,EACtBC,uBAAuB,EACvBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,iBAAiB,QACZ,sBAAmB;AAE1B,MAAMC,mBAAmB,GAAG,EAAE;AAC9B,MAAMC,kBAAkB,GAAG,EAAE;AAC7B,MAAMC,kBAAkB,GAAG,EAAE;;AAE7B;AACA;AACA;AACA,OAAO,MAAMC,iBAAmC,GAAG;EACjDC,gBAAgB,EAAE,YAAY;EAC9BC,cAAc,EAAE;IACdC,IAAI,EAAEP,iBAAiB;IACvBQ,KAAK,EAAER;EACT,CAAC;EACDS,qBAAqB,EAAEtB,gBAAgB;EACvCuB,uBAAuB,EAAEjB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMkB,uBAAyC,GAAG;EACvDN,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEP,iBAAiB;IACvBQ,KAAK,EAAER;EACT,CAAC;EACDS,qBAAqB,EAAEjB,cAAc;EACrCkB,uBAAuB,EAAEjB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMmB,oBAAsC,GAAG;EACpDP,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEP,iBAAiB;IACvBQ,KAAK,EAAER;EACT,CAAC;EACDS,qBAAqB,EAAEpB,uBAAuB;EAC9CqB,uBAAuB,EAAEjB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMoB,qBAAuC,GAAG;EACrDR,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEX,2BAA2B;IACjCY,KAAK,EAAEX;EACT,CAAC;EACDY,qBAAqB,EAAE1B,wBAAwB;EAC/C2B,uBAAuB,EAAEjB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMqB,uBAAyC,GAAG;EACvDT,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAET,2BAA2B;IACjCU,KAAK,EAAEV;EACT,CAAC;EACDW,qBAAqB,EAAEnB,0BAA0B;EACjDoB,uBAAuB,EAAEjB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMsB,sBAAwC,GAAG;EACtDV,gBAAgB,EAAE,YAAY;EAC9BC,cAAc,EAAE;IACdC,IAAI,EAAER,0BAA0B;IAChCS,KAAK,EAAET;EACT,CAAC;EACDU,qBAAqB,EAAElB,yBAAyB;EAChDmB,uBAAuB,EAAEjB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMuB,oBAAsC,GAAG;EACpDX,gBAAgB,EAAE,YAAY;EAC9BC,cAAc,EAAE;IACdC,IAAI,EAAEX,2BAA2B;IACjCY,KAAK,EAAEX;EACT,CAAC;EACDY,qBAAqB,EAAEvB,uBAAuB;EAC9CwB,uBAAuB,EAAEjB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMwB,kBAAoC,GAAG;EAClDZ,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEb,sBAAsB;IAC5Bc,KAAK,EAAEb;EACT,CAAC;EACDc,qBAAqB,EAAE3B,qBAAqB;EAC5C4B,uBAAuB,EAAEjB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMyB,mBAAqC,GAAG;EACnDb,gBAAgB,EAAE,UAAU;EAC5BC,cAAc,EAAE;IACdC,IAAI,EAAEb,sBAAsB;IAC5Bc,KAAK,EAAEb;EACT,CAAC;EACDc,qBAAqB,EAAExB,WAAW;EAClCyB,uBAAuB,EAAEjB;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM0B,iBAAiB,GAAGtC,QAAQ,CAACuC,MAAM,CAAC;EAC/CC,GAAG,EAAEjB,iBAAiB;EACtBkB,OAAO,EACLC,MAAM,CAAC1C,QAAQ,CAAC2C,OAAO,CAAC,IAAIrB,kBAAkB,GAC1Ca,oBAAoB,GACpBO,MAAM,CAAC1C,QAAQ,CAAC2C,OAAO,CAAC,IAAItB,kBAAkB,GAC5Ca,sBAAsB,GACtBQ,MAAM,CAAC1C,QAAQ,CAAC2C,OAAO,CAAC,IAAIvB,mBAAmB,GAC7Ca,uBAAuB,GACvBD,qBAAqB;EAC/BY,OAAO,EAAEV;AACX,CAAC,CAAC;;AAEF;AACA;AACA;AACA,OAAO,MAAMW,eAAe,GAAG7C,QAAQ,CAACuC,MAAM,CAAC;EAC7CC,GAAG,EAAET,oBAAoB;EACzBa,OAAO,EAAER;AACX,CAAC,CAAC;;AAEF;AACA;AACA;AACA,OAAO,MAAMU,gBAAkC,GAAG;EAChD,GAAGvB,iBAAiB;EACpBK,qBAAqB,EAAErB;AACzB,CAAC", "ignoreList": []}